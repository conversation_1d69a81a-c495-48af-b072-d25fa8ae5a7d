﻿using System;
using System.Collections.Generic;
using System.Linq;

Console.WriteLine("Testing GoalGroup API Mapping...");

// Test the mapping logic that was causing the error
TestMappingLogic();

Console.WriteLine("Test completed. Press any key to exit...");
Console.ReadKey();

static void TestMappingLogic()
{
    Console.WriteLine("\n=== Testing Mapping Logic ===");

    // Create test data similar to what the API would have
    var goalGroups = new List<Cat_GoalGroup>
    {
        new Cat_GoalGroup
        {
            Id = Guid.NewGuid(),
            Code = "GG001",
            Name = "Goal Group 1",
            Description = "Test Goal Group 1"
        },
        new Cat_GoalGroup
        {
            Id = Guid.NewGuid(),
            Code = "GG002",
            Name = "Goal Group 2",
            Description = "Test Goal Group 2"
        }
    };

    Console.WriteLine($"Created {goalGroups.Count} test GoalGroups");

    // Simulate what the QueryHandler was trying to do
    try
    {
        // This is what was failing: trying to map List<Cat_GoalGroup> to ListGoalGroupDto (single object)
        // var result = mapper.Map<ListGoalGroupDto>(goalGroups); // This would fail

        // This is what should happen: map to collection
        var result = MapToCollection(goalGroups);
        Console.WriteLine($"Successfully mapped to {result.Count()} DTOs");

        foreach (var dto in result)
        {
            Console.WriteLine($"  - {dto.Code}: {dto.Name}");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Mapping Error: {ex.Message}");
    }
}

static IEnumerable<ListGoalGroupDto> MapToCollection(IEnumerable<Cat_GoalGroup> entities)
{
    // Manual mapping to simulate AutoMapper
    return entities.Select(e => new ListGoalGroupDto
    {
        ID = e.Id,
        Code = e.Code,
        Name = e.Name,
        Description = e.Description
    });
}

// Simplified classes for testing
public class Cat_GoalGroup
{
    public Guid Id { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
}

public class ListGoalGroupDto
{
    public Guid ID { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
}
