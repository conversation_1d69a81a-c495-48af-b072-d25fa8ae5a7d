﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35527.113
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Src", "Src", "{4BF15EBD-F36A-4737-9481-49FA96378098}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{653DDDD7-C01F-4159-A218-396B7A30AA50}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VNR.Core.Tests", "Tests\VNR.Core.Tests\VNR.Core.Tests.csproj", "{1B839D23-D267-4A3A-9385-EEBEF89AEFA5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VNR.Infrastructure.Tests", "Tests\VNR.Infrastructure.Tests\VNR.Infrastructure.Tests.csproj", "{0EC32745-48A7-41AF-9D6F-A421F8DEF964}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VNR.Modules.ModuleA.Tests", "Tests\VNR.Modules.ModuleA.Tests\VNR.Modules.ModuleA.Tests.csproj", "{D2E39D6B-56EE-4115-8742-B3850DB40805}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VNR.Modules.ModuleB.Tests", "Tests\VNR.Modules.ModuleB.Tests\VNR.Modules.ModuleB.Tests.csproj", "{C59E6A0B-5CAA-473E-8193-3104307FF6B0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Cores", "Cores", "{0CD75250-B330-4638-8A9E-8EDA1ABBF58D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{69C58D02-CC2F-4B1A-B3BD-BBC1D9CE543F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core", "Src\Cores\VNR.Core\VNR.Core.csproj", "{864B5441-5E6C-4B5B-BCA6-DCF65254DD5E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core.Api", "Src\Cores\VNR.Core.Api\VNR.Core.Api.csproj", "{03C1A3DF-BBDA-4C2C-B62A-6A8AA93B94C8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core.Models", "Src\Cores\VNR.Core.Models\VNR.Core.Models.csproj", "{0B66E9DB-E34A-4698-BE90-E71651EECB75}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{643E419E-27B0-495F-B121-01EA0FCD597D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core.Domain", "Src\Cores\VNR.Core.Domain\VNR.Core.Domain.csproj", "{14ABF137-31DF-453E-BF41-71849D64C64D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Infrastructure.Persistence", "Src\Infrastructure\VNR.Infrastructure.Persistence\VNR.Infrastructure.Persistence.csproj", "{3094A8F8-9A22-4A1E-B68F-253D6BA5E87D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Hosting", "Hosting", "{4A28C308-30C0-40E3-995E-E8A2DB25312D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Hosting.CompositionRoot", "Src\Hosting\VNR.Hosting.CompositionRoot\VNR.Hosting.CompositionRoot.csproj", "{3537AB8B-6836-4676-A76A-FC1EE0A2F378}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Hosting.WorkerHost", "Src\Hosting\VNR.Hosting.WorkerHost\VNR.Hosting.WorkerHost.csproj", "{74EAA35A-B006-46D0-9B4B-83429B1EE07B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core.Application", "Src\Cores\VNR.Core.Application\VNR.Core.Application.csproj", "{C558F507-E7D3-4251-B46F-7A31556E5CC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Infrastructure.BaseRepositories", "Src\Infrastructure\VNR.Infrastructure.BaseRepositories\VNR.Infrastructure.BaseRepositories.csproj", "{F73A96FB-DA21-48D7-9FA5-FD631CC71318}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sample", "Sample", "{F6920FC1-A9E5-4421-985A-9127941415C5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Sample.Api", "Src\Services\Sample\VNR.Service.Sample.Api\VNR.Service.Sample.Api.csproj", "{03DB909C-B70A-49C5-8DD9-68E070CC1C70}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Sample.Domain", "Src\Services\Sample\VNR.Service.Sample.Domain\VNR.Service.Sample.Domain.csproj", "{10DB769C-9728-447E-9434-EB877FC1BDF9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Sample.Application", "Src\Services\Sample\VNR.Service.Sample.Application\VNR.Service.Sample.Application.csproj", "{099BB525-14B0-44E9-B16E-6687525B6D6E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Sample.Infrastructure", "Src\Services\Sample\VNR.Service.Sample.Infrastructure\VNR.Service.Sample.Infrastructure.csproj", "{9A409692-505C-41AC-B935-6C70AC02221D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Config", "Config", "{DD13D5B7-27DA-45DB-ADA3-D6726834CE42}"
	ProjectSection(SolutionItems) = preProject
		Config\AppSetting.json = Config\AppSetting.json
		Config\Authentication.json = Config\Authentication.json
		Config\Cache.json = Config\Cache.json
		Config\Connections.json = Config\Connections.json
		Config\Integration.json = Config\Integration.json
		Config\Security.json = Config\Security.json
		Config\Serilog.json = Config\Serilog.json
		Config\Sync.json = Config\Sync.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Resources", "Resources", "{DE24EC6F-1148-4998-8DC8-B52A0DD90FDC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{0C6C81AE-8DBC-4EDC-9611-999896A176FF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "i18n", "i18n", "{F6540CA7-690C-4799-B950-91EB79900ECD}"
	ProjectSection(SolutionItems) = preProject
		Resources\i18n\i18nCommon.json = Resources\i18n\i18nCommon.json
		Resources\i18n\i18nValidate.json = Resources\i18n\i18nValidate.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Settings", "Settings", "{F0FA64F1-54AB-4C18-82CB-5582D9967AD9}"
	ProjectSection(SolutionItems) = preProject
		DetailConfig.json = DetailConfig.json
		FormConfig.json = FormConfig.json
		Resources\Settings\GridConfig.json = Resources\Settings\GridConfig.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Uploads", "Uploads", "{007D346F-BD65-441A-8A60-820028BA8E58}"
	ProjectSection(SolutionItems) = preProject
		Resources\Uploads\TempUpload.docx = Resources\Uploads\TempUpload.docx
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Templates", "Templates", "{CDCA464D-53A5-4D36-B71E-3E455556AA27}"
	ProjectSection(SolutionItems) = preProject
		Resources\Templates\TempTemplate.docx = Resources\Templates\TempTemplate.docx
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Logs", "Logs", "{044B5890-D6E2-490F-8FF9-32FAEB29F7FA}"
	ProjectSection(SolutionItems) = preProject
		Logs\temp-log.txt = Logs\temp-log.txt
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Download", "Download", "{20A9984B-19B8-46D2-A53F-618077B0D0A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core.Common", "Src\Cores\VNR.Core.Common\VNR.Core.Common.csproj", "{3B8CD137-5E49-4653-8E79-03CD04964B34}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Infrastructure.Security", "Src\Infrastructure\VNR.Infrastructure.Security\VNR.Infrastructure.Security.csproj", "{AAFA130E-E053-4B47-BAEB-BD6A8137BCCF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core.Security", "Src\Cores\VNR.Core.Security\VNR.Core.Security.csproj", "{EEDCBCCA-3B5B-406B-92DD-DA407FECD4AA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "VNR.Service.Sample.Api", "VNR.Service.Sample.Api", "{010F1523-2D30-4DA4-A9F2-10EACF11D230}"
	ProjectSection(SolutionItems) = preProject
		Config\Services\VNR.Service.Sample.Api\appsettings.Development.json = Config\Services\VNR.Service.Sample.Api\appsettings.Development.json
		Config\Services\VNR.Service.Sample.Api\appsettings.json = Config\Services\VNR.Service.Sample.Api\appsettings.json
		Config\Services\VNR.Service.Sample.Api\appsettings.Production.json = Config\Services\VNR.Service.Sample.Api\appsettings.Production.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Monitoring", "Monitoring", "{C65F2532-2AEC-444E-9016-B870F4230B76}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Monitoring.HealthChecks.UI", "Src\Monitoring\VNR.Monitoring.HealthChecks.UI\VNR.Monitoring.HealthChecks.UI.csproj", "{81397EAC-A6A5-4EF8-B5F2-5936BD0EC996}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "VNR.Monitoring.HealthChecks.UI", "VNR.Monitoring.HealthChecks.UI", "{253E1376-F647-4030-9D42-055CCD8A0204}"
	ProjectSection(SolutionItems) = preProject
		Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Development.json = Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Development.json
		Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.json = Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.json
		Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Production.json = Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Production.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Infrastructure.Queue", "Src\Infrastructure\VNR.Infrastructure.Queue\VNR.Infrastructure.Queue.csproj", "{10CF926C-B5D2-467E-ABDC-148B488ADE28}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Sample.Worker", "Src\Services\Sample\VNR.Service.Sample.Worker\VNR.Service.Sample.Worker.csproj", "{90487CDA-5939-4C20-B23C-A327F0F99873}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Sample.Worker.Application", "Src\Services\Sample\VNR.Service.Sample.Worker.Application\VNR.Service.Sample.Worker.Application.csproj", "{4BE4E1BF-9063-430E-ABC0-6B1520FB6D99}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Infrastructure.Logging", "Src\Infrastructure\VNR.Infrastructure.Logging\VNR.Infrastructure.Logging.csproj", "{13A636B5-239E-4054-AAB2-271D0E6A9258}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Sample.Models", "Src\Services\Sample\VNR.Service.Sample.Models\VNR.Service.Sample.Models.csproj", "{4952E665-0203-4A54-BD1C-18A51993623B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Core.Configurations", "Src\Cores\VNR.Core.Configurations\VNR.Core.Configurations.csproj", "{3F632253-CDBE-43C5-B7E3-EDDAD93AA224}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Infrastructure.EntityFramework.PostgreSQL", "Src\Infrastructure\VNR.Infrastructure.EntityFramework.PostgreSQL\VNR.Infrastructure.EntityFramework.PostgreSQL.csproj", "{0573D601-3DDA-40FB-8199-4BF1DB4FD14D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Infrastructure.EntityFramework.SqlServer", "Src\Infrastructure\VNR.Infrastructure.EntityFramework.SqlServer\VNR.Infrastructure.EntityFramework.SqlServer.csproj", "{9D413B4A-F52C-4EA3-A321-5D5E0C94A78B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "IdentityServer", "IdentityServer", "{C6696F36-8AF1-4C3F-B619-F06C34E57558}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.IdentityServer.Identity", "Src\Services\IdentityServer\VNR.Service.IdentityServer.Identity\VNR.Service.IdentityServer.Identity.csproj", "{C087FA7B-9D86-4DAA-8D38-1586832F23D8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "VNR.Service.IdentityServer.Identity", "VNR.Service.IdentityServer.Identity", "{E86B0E2B-DA84-440F-8250-2C27D9B2F3F4}"
	ProjectSection(SolutionItems) = preProject
		Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Development.json = Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Development.json
		Config\Services\VNR.Service.IdentityServer.Identity\appsettings.json = Config\Services\VNR.Service.IdentityServer.Identity\appsettings.json
		Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Production.json = Config\Services\VNR.Monitoring.HealthChecks.UI\appsettings.Production.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{80BFAF84-3457-44B9-87AE-5B317613B947}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.IdentityServer.Shared.Configuration", "Src\Services\IdentityServer\Shared\VNR.Service.IdentityServer.Shared.Configuration\VNR.Service.IdentityServer.Shared.Configuration.csproj", "{CC0C65DA-DF32-4392-B481-95DDE2AA59A9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{32C87D59-3BF8-4ECB-8267-D9144EDC902E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Shared.Api", "Src\Services\Shared\VNR.Service.Shared.Api\VNR.Service.Shared.Api.csproj", "{374C4CA5-E29C-4F7C-B2DD-2D1136C27994}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Shared.Application", "Src\Services\Shared\VNR.Service.Shared.Application\VNR.Service.Shared.Application.csproj", "{FC202EB5-870A-453B-B872-D80465D18461}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Shared.Domain", "Src\Services\Shared\VNR.Service.Shared.Domain\VNR.Service.Shared.Domain.csproj", "{278A0318-2E28-4617-BA22-0D78B41E9992}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Shared.Infrastructure", "Src\Services\Shared\VNR.Service.Shared.Infrastructure\VNR.Service.Shared.Infrastructure.csproj", "{C3400007-7ECE-4060-A7EE-AC10AC8D7A02}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Shared.Models", "Src\Services\Shared\VNR.Service.Shared.Models\VNR.Service.Shared.Models.csproj", "{09A5E4DF-23ED-4DED-80D5-E9A4D2E720DA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "VNR.Service.Shared.Api", "VNR.Service.Shared.Api", "{07923755-54D1-4B54-A95A-891DC0BD3F96}"
	ProjectSection(SolutionItems) = preProject
		Config\Services\VNR.Service.Shared.Api\appsettings.Development.json = Config\Services\VNR.Service.Shared.Api\appsettings.Development.json
		Config\Services\VNR.Service.Shared.Api\appsettings.json = Config\Services\VNR.Service.Shared.Api\appsettings.json
		Config\Services\VNR.Service.Shared.Api\appsettings.Production.json = Config\Services\VNR.Service.Shared.Api\appsettings.Production.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Evaluation", "Evaluation", "{DC82B5A7-6090-4B38-99B9-8C76B68ABA40}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Evaluation.Api", "Src\Services\Evaluation\VNR.Service.Evaluation.Api\VNR.Service.Evaluation.Api.csproj", "{398CDDF8-4B81-4600-9D2F-D5D486E4FF36}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Evaluation.Application", "Src\Services\Evaluation\VNR.Service.Evaluation.Application\VNR.Service.Evaluation.Application.csproj", "{FC9511A4-7D11-403C-B576-E549FD21C6C5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Evaluation.Domain", "Src\Services\Evaluation\VNR.Service.Evaluation.Domain\VNR.Service.Evaluation.Domain.csproj", "{2F1687E7-4C6F-425C-AA48-B96D35D292D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Evaluation.Infrastructure", "Src\Services\Evaluation\VNR.Service.Evaluation.Infrastructure\VNR.Service.Evaluation.Infrastructure.csproj", "{1D6321E9-CF03-4DAB-A600-6340F50EB6BB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "VNR.Service.Evaluation.Models", "Src\Services\Evaluation\VNR.Service.Evaluation.Models\VNR.Service.Evaluation.Models.csproj", "{3C198783-61E0-4134-A57C-1E0C7C149742}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "VNR.Service.Evaluation.Api", "VNR.Service.Evaluation.Api", "{666542C5-DBDB-4952-9077-A524F7F80795}"
	ProjectSection(SolutionItems) = preProject
		Config\Services\VNR.Service.Evaluation.Api\appsettings.Development.json = Config\Services\VNR.Service.Evaluation.Api\appsettings.Development.json
		Config\Services\VNR.Service.Evaluation.Api\appsettings.json = Config\Services\VNR.Service.Evaluation.Api\appsettings.json
		Config\Services\VNR.Service.Evaluation.Api\appsettings.Production.json = Config\Services\VNR.Service.Evaluation.Api\appsettings.Production.json
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1B839D23-D267-4A3A-9385-EEBEF89AEFA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B839D23-D267-4A3A-9385-EEBEF89AEFA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B839D23-D267-4A3A-9385-EEBEF89AEFA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B839D23-D267-4A3A-9385-EEBEF89AEFA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{0EC32745-48A7-41AF-9D6F-A421F8DEF964}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0EC32745-48A7-41AF-9D6F-A421F8DEF964}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0EC32745-48A7-41AF-9D6F-A421F8DEF964}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0EC32745-48A7-41AF-9D6F-A421F8DEF964}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2E39D6B-56EE-4115-8742-B3850DB40805}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2E39D6B-56EE-4115-8742-B3850DB40805}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2E39D6B-56EE-4115-8742-B3850DB40805}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2E39D6B-56EE-4115-8742-B3850DB40805}.Release|Any CPU.Build.0 = Release|Any CPU
		{C59E6A0B-5CAA-473E-8193-3104307FF6B0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C59E6A0B-5CAA-473E-8193-3104307FF6B0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C59E6A0B-5CAA-473E-8193-3104307FF6B0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C59E6A0B-5CAA-473E-8193-3104307FF6B0}.Release|Any CPU.Build.0 = Release|Any CPU
		{864B5441-5E6C-4B5B-BCA6-DCF65254DD5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{864B5441-5E6C-4B5B-BCA6-DCF65254DD5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{864B5441-5E6C-4B5B-BCA6-DCF65254DD5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{864B5441-5E6C-4B5B-BCA6-DCF65254DD5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{03C1A3DF-BBDA-4C2C-B62A-6A8AA93B94C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{03C1A3DF-BBDA-4C2C-B62A-6A8AA93B94C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{03C1A3DF-BBDA-4C2C-B62A-6A8AA93B94C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{03C1A3DF-BBDA-4C2C-B62A-6A8AA93B94C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B66E9DB-E34A-4698-BE90-E71651EECB75}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B66E9DB-E34A-4698-BE90-E71651EECB75}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B66E9DB-E34A-4698-BE90-E71651EECB75}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B66E9DB-E34A-4698-BE90-E71651EECB75}.Release|Any CPU.Build.0 = Release|Any CPU
		{14ABF137-31DF-453E-BF41-71849D64C64D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{14ABF137-31DF-453E-BF41-71849D64C64D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{14ABF137-31DF-453E-BF41-71849D64C64D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{14ABF137-31DF-453E-BF41-71849D64C64D}.Release|Any CPU.Build.0 = Release|Any CPU
		{3094A8F8-9A22-4A1E-B68F-253D6BA5E87D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3094A8F8-9A22-4A1E-B68F-253D6BA5E87D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3094A8F8-9A22-4A1E-B68F-253D6BA5E87D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3094A8F8-9A22-4A1E-B68F-253D6BA5E87D}.Release|Any CPU.Build.0 = Release|Any CPU
		{3537AB8B-6836-4676-A76A-FC1EE0A2F378}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3537AB8B-6836-4676-A76A-FC1EE0A2F378}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3537AB8B-6836-4676-A76A-FC1EE0A2F378}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3537AB8B-6836-4676-A76A-FC1EE0A2F378}.Release|Any CPU.Build.0 = Release|Any CPU
		{74EAA35A-B006-46D0-9B4B-83429B1EE07B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74EAA35A-B006-46D0-9B4B-83429B1EE07B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74EAA35A-B006-46D0-9B4B-83429B1EE07B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74EAA35A-B006-46D0-9B4B-83429B1EE07B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C558F507-E7D3-4251-B46F-7A31556E5CC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C558F507-E7D3-4251-B46F-7A31556E5CC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C558F507-E7D3-4251-B46F-7A31556E5CC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C558F507-E7D3-4251-B46F-7A31556E5CC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{F73A96FB-DA21-48D7-9FA5-FD631CC71318}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F73A96FB-DA21-48D7-9FA5-FD631CC71318}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F73A96FB-DA21-48D7-9FA5-FD631CC71318}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F73A96FB-DA21-48D7-9FA5-FD631CC71318}.Release|Any CPU.Build.0 = Release|Any CPU
		{03DB909C-B70A-49C5-8DD9-68E070CC1C70}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{03DB909C-B70A-49C5-8DD9-68E070CC1C70}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{03DB909C-B70A-49C5-8DD9-68E070CC1C70}.Release|Any CPU.Build.0 = Release|Any CPU
		{10DB769C-9728-447E-9434-EB877FC1BDF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10DB769C-9728-447E-9434-EB877FC1BDF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10DB769C-9728-447E-9434-EB877FC1BDF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{099BB525-14B0-44E9-B16E-6687525B6D6E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{099BB525-14B0-44E9-B16E-6687525B6D6E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{099BB525-14B0-44E9-B16E-6687525B6D6E}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A409692-505C-41AC-B935-6C70AC02221D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A409692-505C-41AC-B935-6C70AC02221D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A409692-505C-41AC-B935-6C70AC02221D}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B8CD137-5E49-4653-8E79-03CD04964B34}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B8CD137-5E49-4653-8E79-03CD04964B34}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B8CD137-5E49-4653-8E79-03CD04964B34}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B8CD137-5E49-4653-8E79-03CD04964B34}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAFA130E-E053-4B47-BAEB-BD6A8137BCCF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAFA130E-E053-4B47-BAEB-BD6A8137BCCF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAFA130E-E053-4B47-BAEB-BD6A8137BCCF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAFA130E-E053-4B47-BAEB-BD6A8137BCCF}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEDCBCCA-3B5B-406B-92DD-DA407FECD4AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEDCBCCA-3B5B-406B-92DD-DA407FECD4AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEDCBCCA-3B5B-406B-92DD-DA407FECD4AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEDCBCCA-3B5B-406B-92DD-DA407FECD4AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{81397EAC-A6A5-4EF8-B5F2-5936BD0EC996}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81397EAC-A6A5-4EF8-B5F2-5936BD0EC996}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81397EAC-A6A5-4EF8-B5F2-5936BD0EC996}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81397EAC-A6A5-4EF8-B5F2-5936BD0EC996}.Release|Any CPU.Build.0 = Release|Any CPU
		{10CF926C-B5D2-467E-ABDC-148B488ADE28}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10CF926C-B5D2-467E-ABDC-148B488ADE28}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10CF926C-B5D2-467E-ABDC-148B488ADE28}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10CF926C-B5D2-467E-ABDC-148B488ADE28}.Release|Any CPU.Build.0 = Release|Any CPU
		{90487CDA-5939-4C20-B23C-A327F0F99873}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90487CDA-5939-4C20-B23C-A327F0F99873}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90487CDA-5939-4C20-B23C-A327F0F99873}.Release|Any CPU.Build.0 = Release|Any CPU
		{4BE4E1BF-9063-430E-ABC0-6B1520FB6D99}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4BE4E1BF-9063-430E-ABC0-6B1520FB6D99}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4BE4E1BF-9063-430E-ABC0-6B1520FB6D99}.Release|Any CPU.Build.0 = Release|Any CPU
		{13A636B5-239E-4054-AAB2-271D0E6A9258}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{13A636B5-239E-4054-AAB2-271D0E6A9258}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{13A636B5-239E-4054-AAB2-271D0E6A9258}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{13A636B5-239E-4054-AAB2-271D0E6A9258}.Release|Any CPU.Build.0 = Release|Any CPU
		{4952E665-0203-4A54-BD1C-18A51993623B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4952E665-0203-4A54-BD1C-18A51993623B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4952E665-0203-4A54-BD1C-18A51993623B}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F632253-CDBE-43C5-B7E3-EDDAD93AA224}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F632253-CDBE-43C5-B7E3-EDDAD93AA224}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F632253-CDBE-43C5-B7E3-EDDAD93AA224}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F632253-CDBE-43C5-B7E3-EDDAD93AA224}.Release|Any CPU.Build.0 = Release|Any CPU
		{0573D601-3DDA-40FB-8199-4BF1DB4FD14D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0573D601-3DDA-40FB-8199-4BF1DB4FD14D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0573D601-3DDA-40FB-8199-4BF1DB4FD14D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0573D601-3DDA-40FB-8199-4BF1DB4FD14D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9D413B4A-F52C-4EA3-A321-5D5E0C94A78B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9D413B4A-F52C-4EA3-A321-5D5E0C94A78B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9D413B4A-F52C-4EA3-A321-5D5E0C94A78B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9D413B4A-F52C-4EA3-A321-5D5E0C94A78B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C087FA7B-9D86-4DAA-8D38-1586832F23D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C087FA7B-9D86-4DAA-8D38-1586832F23D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C087FA7B-9D86-4DAA-8D38-1586832F23D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C087FA7B-9D86-4DAA-8D38-1586832F23D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC0C65DA-DF32-4392-B481-95DDE2AA59A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CC0C65DA-DF32-4392-B481-95DDE2AA59A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CC0C65DA-DF32-4392-B481-95DDE2AA59A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CC0C65DA-DF32-4392-B481-95DDE2AA59A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{374C4CA5-E29C-4F7C-B2DD-2D1136C27994}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{374C4CA5-E29C-4F7C-B2DD-2D1136C27994}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{374C4CA5-E29C-4F7C-B2DD-2D1136C27994}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC202EB5-870A-453B-B872-D80465D18461}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC202EB5-870A-453B-B872-D80465D18461}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC202EB5-870A-453B-B872-D80465D18461}.Release|Any CPU.Build.0 = Release|Any CPU
		{278A0318-2E28-4617-BA22-0D78B41E9992}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{278A0318-2E28-4617-BA22-0D78B41E9992}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{278A0318-2E28-4617-BA22-0D78B41E9992}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3400007-7ECE-4060-A7EE-AC10AC8D7A02}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3400007-7ECE-4060-A7EE-AC10AC8D7A02}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3400007-7ECE-4060-A7EE-AC10AC8D7A02}.Release|Any CPU.Build.0 = Release|Any CPU
		{09A5E4DF-23ED-4DED-80D5-E9A4D2E720DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09A5E4DF-23ED-4DED-80D5-E9A4D2E720DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09A5E4DF-23ED-4DED-80D5-E9A4D2E720DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{398CDDF8-4B81-4600-9D2F-D5D486E4FF36}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{398CDDF8-4B81-4600-9D2F-D5D486E4FF36}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{398CDDF8-4B81-4600-9D2F-D5D486E4FF36}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{398CDDF8-4B81-4600-9D2F-D5D486E4FF36}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC9511A4-7D11-403C-B576-E549FD21C6C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC9511A4-7D11-403C-B576-E549FD21C6C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC9511A4-7D11-403C-B576-E549FD21C6C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC9511A4-7D11-403C-B576-E549FD21C6C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{2F1687E7-4C6F-425C-AA48-B96D35D292D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2F1687E7-4C6F-425C-AA48-B96D35D292D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2F1687E7-4C6F-425C-AA48-B96D35D292D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2F1687E7-4C6F-425C-AA48-B96D35D292D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D6321E9-CF03-4DAB-A600-6340F50EB6BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D6321E9-CF03-4DAB-A600-6340F50EB6BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D6321E9-CF03-4DAB-A600-6340F50EB6BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D6321E9-CF03-4DAB-A600-6340F50EB6BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{3C198783-61E0-4134-A57C-1E0C7C149742}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3C198783-61E0-4134-A57C-1E0C7C149742}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3C198783-61E0-4134-A57C-1E0C7C149742}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3C198783-61E0-4134-A57C-1E0C7C149742}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1B839D23-D267-4A3A-9385-EEBEF89AEFA5} = {653DDDD7-C01F-4159-A218-396B7A30AA50}
		{0EC32745-48A7-41AF-9D6F-A421F8DEF964} = {653DDDD7-C01F-4159-A218-396B7A30AA50}
		{D2E39D6B-56EE-4115-8742-B3850DB40805} = {653DDDD7-C01F-4159-A218-396B7A30AA50}
		{C59E6A0B-5CAA-473E-8193-3104307FF6B0} = {653DDDD7-C01F-4159-A218-396B7A30AA50}
		{0CD75250-B330-4638-8A9E-8EDA1ABBF58D} = {4BF15EBD-F36A-4737-9481-49FA96378098}
		{69C58D02-CC2F-4B1A-B3BD-BBC1D9CE543F} = {4BF15EBD-F36A-4737-9481-49FA96378098}
		{864B5441-5E6C-4B5B-BCA6-DCF65254DD5E} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{03C1A3DF-BBDA-4C2C-B62A-6A8AA93B94C8} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{0B66E9DB-E34A-4698-BE90-E71651EECB75} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{643E419E-27B0-495F-B121-01EA0FCD597D} = {4BF15EBD-F36A-4737-9481-49FA96378098}
		{14ABF137-31DF-453E-BF41-71849D64C64D} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{3094A8F8-9A22-4A1E-B68F-253D6BA5E87D} = {643E419E-27B0-495F-B121-01EA0FCD597D}
		{4A28C308-30C0-40E3-995E-E8A2DB25312D} = {4BF15EBD-F36A-4737-9481-49FA96378098}
		{3537AB8B-6836-4676-A76A-FC1EE0A2F378} = {4A28C308-30C0-40E3-995E-E8A2DB25312D}
		{74EAA35A-B006-46D0-9B4B-83429B1EE07B} = {4A28C308-30C0-40E3-995E-E8A2DB25312D}
		{C558F507-E7D3-4251-B46F-7A31556E5CC3} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{F73A96FB-DA21-48D7-9FA5-FD631CC71318} = {643E419E-27B0-495F-B121-01EA0FCD597D}
		{F6920FC1-A9E5-4421-985A-9127941415C5} = {69C58D02-CC2F-4B1A-B3BD-BBC1D9CE543F}
		{03DB909C-B70A-49C5-8DD9-68E070CC1C70} = {F6920FC1-A9E5-4421-985A-9127941415C5}
		{10DB769C-9728-447E-9434-EB877FC1BDF9} = {F6920FC1-A9E5-4421-985A-9127941415C5}
		{099BB525-14B0-44E9-B16E-6687525B6D6E} = {F6920FC1-A9E5-4421-985A-9127941415C5}
		{9A409692-505C-41AC-B935-6C70AC02221D} = {F6920FC1-A9E5-4421-985A-9127941415C5}
		{0C6C81AE-8DBC-4EDC-9611-999896A176FF} = {DD13D5B7-27DA-45DB-ADA3-D6726834CE42}
		{F6540CA7-690C-4799-B950-91EB79900ECD} = {DE24EC6F-1148-4998-8DC8-B52A0DD90FDC}
		{F0FA64F1-54AB-4C18-82CB-5582D9967AD9} = {DE24EC6F-1148-4998-8DC8-B52A0DD90FDC}
		{007D346F-BD65-441A-8A60-820028BA8E58} = {DE24EC6F-1148-4998-8DC8-B52A0DD90FDC}
		{CDCA464D-53A5-4D36-B71E-3E455556AA27} = {DE24EC6F-1148-4998-8DC8-B52A0DD90FDC}
		{20A9984B-19B8-46D2-A53F-618077B0D0A5} = {DE24EC6F-1148-4998-8DC8-B52A0DD90FDC}
		{3B8CD137-5E49-4653-8E79-03CD04964B34} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{AAFA130E-E053-4B47-BAEB-BD6A8137BCCF} = {643E419E-27B0-495F-B121-01EA0FCD597D}
		{EEDCBCCA-3B5B-406B-92DD-DA407FECD4AA} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{010F1523-2D30-4DA4-A9F2-10EACF11D230} = {0C6C81AE-8DBC-4EDC-9611-999896A176FF}
		{C65F2532-2AEC-444E-9016-B870F4230B76} = {4BF15EBD-F36A-4737-9481-49FA96378098}
		{81397EAC-A6A5-4EF8-B5F2-5936BD0EC996} = {C65F2532-2AEC-444E-9016-B870F4230B76}
		{253E1376-F647-4030-9D42-055CCD8A0204} = {0C6C81AE-8DBC-4EDC-9611-999896A176FF}
		{10CF926C-B5D2-467E-ABDC-148B488ADE28} = {643E419E-27B0-495F-B121-01EA0FCD597D}
		{90487CDA-5939-4C20-B23C-A327F0F99873} = {F6920FC1-A9E5-4421-985A-9127941415C5}
		{4BE4E1BF-9063-430E-ABC0-6B1520FB6D99} = {F6920FC1-A9E5-4421-985A-9127941415C5}
		{13A636B5-239E-4054-AAB2-271D0E6A9258} = {643E419E-27B0-495F-B121-01EA0FCD597D}
		{4952E665-0203-4A54-BD1C-18A51993623B} = {F6920FC1-A9E5-4421-985A-9127941415C5}
		{3F632253-CDBE-43C5-B7E3-EDDAD93AA224} = {0CD75250-B330-4638-8A9E-8EDA1ABBF58D}
		{0573D601-3DDA-40FB-8199-4BF1DB4FD14D} = {643E419E-27B0-495F-B121-01EA0FCD597D}
		{9D413B4A-F52C-4EA3-A321-5D5E0C94A78B} = {643E419E-27B0-495F-B121-01EA0FCD597D}
		{C6696F36-8AF1-4C3F-B619-F06C34E57558} = {69C58D02-CC2F-4B1A-B3BD-BBC1D9CE543F}
		{C087FA7B-9D86-4DAA-8D38-1586832F23D8} = {C6696F36-8AF1-4C3F-B619-F06C34E57558}
		{E86B0E2B-DA84-440F-8250-2C27D9B2F3F4} = {0C6C81AE-8DBC-4EDC-9611-999896A176FF}
		{80BFAF84-3457-44B9-87AE-5B317613B947} = {C6696F36-8AF1-4C3F-B619-F06C34E57558}
		{CC0C65DA-DF32-4392-B481-95DDE2AA59A9} = {80BFAF84-3457-44B9-87AE-5B317613B947}
		{32C87D59-3BF8-4ECB-8267-D9144EDC902E} = {69C58D02-CC2F-4B1A-B3BD-BBC1D9CE543F}
		{374C4CA5-E29C-4F7C-B2DD-2D1136C27994} = {32C87D59-3BF8-4ECB-8267-D9144EDC902E}
		{FC202EB5-870A-453B-B872-D80465D18461} = {32C87D59-3BF8-4ECB-8267-D9144EDC902E}
		{278A0318-2E28-4617-BA22-0D78B41E9992} = {32C87D59-3BF8-4ECB-8267-D9144EDC902E}
		{C3400007-7ECE-4060-A7EE-AC10AC8D7A02} = {32C87D59-3BF8-4ECB-8267-D9144EDC902E}
		{09A5E4DF-23ED-4DED-80D5-E9A4D2E720DA} = {32C87D59-3BF8-4ECB-8267-D9144EDC902E}
		{07923755-54D1-4B54-A95A-891DC0BD3F96} = {0C6C81AE-8DBC-4EDC-9611-999896A176FF}
		{DC82B5A7-6090-4B38-99B9-8C76B68ABA40} = {69C58D02-CC2F-4B1A-B3BD-BBC1D9CE543F}
		{398CDDF8-4B81-4600-9D2F-D5D486E4FF36} = {DC82B5A7-6090-4B38-99B9-8C76B68ABA40}
		{FC9511A4-7D11-403C-B576-E549FD21C6C5} = {DC82B5A7-6090-4B38-99B9-8C76B68ABA40}
		{2F1687E7-4C6F-425C-AA48-B96D35D292D8} = {DC82B5A7-6090-4B38-99B9-8C76B68ABA40}
		{1D6321E9-CF03-4DAB-A600-6340F50EB6BB} = {DC82B5A7-6090-4B38-99B9-8C76B68ABA40}
		{3C198783-61E0-4134-A57C-1E0C7C149742} = {DC82B5A7-6090-4B38-99B9-8C76B68ABA40}
		{666542C5-DBDB-4952-9077-A524F7F80795} = {0C6C81AE-8DBC-4EDC-9611-999896A176FF}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D76ACF17-9C01-4C79-85A4-D608F8530A40}
	EndGlobalSection
EndGlobal
