using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Category.Services;
using VNR.Service.Evaluation.Models.Category.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Category.Queries;

public class ListGoalGroupQueryHandler : QueryListHandler<ListGoalGroupQuery, ListGoalGroupDto>
{
    private readonly IApplicationContext _context;
    private readonly ICat_GoalGroupService _service;

    public ListGoalGroupQueryHandler(ICat_GoalGroupService Cat_GoalGroupService, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_context, _service) = (applicationContext, Cat_GoalGroupService);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListGoalGroupDto>>> Handle(ListGoalGroupQuery request,
        CancellationToken cancellationToken)
    {
        var listData = await _service.GetAllAsync();

        var result = _context.Mapper.Map<IEnumerable<ListGoalGroupDto>>(listData);

        await _context.LoggingService.LogInformationAsync($"Retrieved {listData.Count()} GoalGroup records");

        return ResultKendoGrid(request.Request, result);
    }
}