using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using VNR.Core.Domain.Entities.Category;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Infrastructure.Logging.Services;
using VNR.Service.Evaluation.Application.Category.Queries;
using VNR.Service.Evaluation.Application.Category.Services;
using VNR.Service.Evaluation.Models.Category.ResponseDtos;

namespace TestGoalGroupApi
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing GoalGroup API...");

            // Test AutoMapper mapping
            TestAutoMapperMapping();

            // Test QueryHandler logic
            await TestQueryHandlerLogic();

            Console.WriteLine("Test completed. Press any key to exit...");
            Console.ReadKey();
        }

        static void TestAutoMapperMapping()
        {
            Console.WriteLine("\n=== Testing AutoMapper Mapping ===");

            var config = new MapperConfiguration(cfg =>
            {
                // Simulate the mapping configuration
                cfg.CreateMap<Cat_GoalGroup, ListGoalGroupDto>()
                    .ForMember(dest => dest.ID, opt => opt.MapFrom(src => src.Id))
                    .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                    .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description));
            });

            var mapper = config.CreateMapper();

            // Create test data
            var goalGroups = new List<Cat_GoalGroup>
            {
                new Cat_GoalGroup
                {
                    Id = Guid.NewGuid(),
                    Code = "GG001",
                    Name = "Goal Group 1",
                    Description = "Test Goal Group 1",
                    IsActive = true
                },
                new Cat_GoalGroup
                {
                    Id = Guid.NewGuid(),
                    Code = "GG002", 
                    Name = "Goal Group 2",
                    Description = "Test Goal Group 2",
                    IsActive = true
                }
            };

            try
            {
                // Test single mapping
                var singleDto = mapper.Map<ListGoalGroupDto>(goalGroups.First());
                Console.WriteLine($"Single mapping successful: {singleDto.Name}");

                // Test collection mapping
                var collectionDto = mapper.Map<IEnumerable<ListGoalGroupDto>>(goalGroups);
                Console.WriteLine($"Collection mapping successful: {collectionDto.Count()} items");

                foreach (var dto in collectionDto)
                {
                    Console.WriteLine($"  - {dto.Code}: {dto.Name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AutoMapper Error: {ex.Message}");
            }
        }

        static async Task TestQueryHandlerLogic()
        {
            Console.WriteLine("\n=== Testing QueryHandler Logic ===");

            // Mock dependencies
            var mockService = new Mock<ICat_GoalGroupService>();
            var mockContext = new Mock<IApplicationContext>();
            var mockMapper = new Mock<IMapper>();
            var mockLoggingService = new Mock<ILoggingService>();

            // Setup test data
            var testData = new List<Cat_GoalGroup>
            {
                new Cat_GoalGroup
                {
                    Id = Guid.NewGuid(),
                    Code = "GG001",
                    Name = "Goal Group 1",
                    Description = "Test Goal Group 1"
                }
            };

            var expectedDtos = new List<ListGoalGroupDto>
            {
                new ListGoalGroupDto
                {
                    ID = testData.First().Id,
                    Code = "GG001",
                    Name = "Goal Group 1",
                    Description = "Test Goal Group 1"
                }
            };

            // Setup mocks
            mockService.Setup(s => s.GetAllAsync()).ReturnsAsync(testData);
            mockMapper.Setup(m => m.Map<IEnumerable<ListGoalGroupDto>>(It.IsAny<IEnumerable<Cat_GoalGroup>>()))
                     .Returns(expectedDtos);
            mockContext.Setup(c => c.Mapper).Returns(mockMapper.Object);
            mockContext.Setup(c => c.LoggingService).Returns(mockLoggingService.Object);

            try
            {
                // Simulate the QueryHandler logic
                var listData = await mockService.Object.GetAllAsync();
                Console.WriteLine($"Service returned {listData.Count()} items");

                var result = mockContext.Object.Mapper.Map<IEnumerable<ListGoalGroupDto>>(listData);
                Console.WriteLine($"Mapping returned {result.Count()} DTOs");

                foreach (var dto in result)
                {
                    Console.WriteLine($"  - {dto.Code}: {dto.Name}");
                }

                Console.WriteLine("QueryHandler logic test successful!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"QueryHandler Error: {ex.Message}");
            }
        }
    }

    // Simplified entity classes for testing
    public class Cat_GoalGroup
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
    }

    public class ListGoalGroupDto
    {
        public Guid ID { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }
}
